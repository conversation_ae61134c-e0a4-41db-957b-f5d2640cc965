{"format_version": "1.8.0", "animations": {"animation.ithiefi_ditsh_alternate.twitch": {"loop": true, "animation_length": 7.1668, "bones": {"head": {"rotation": {"2.875": [0, 0, 0], "2.9583": {"pre": [0, 0, 0], "post": [0, 0, -22.5]}, "3.0833": {"pre": [0, 0, -22.5], "post": [-7.5, 0, -22.5]}, "3.375": {"pre": [-7.5, 0, -22.5], "post": [-0.98448, 7.43547, 59.93603]}, "3.7083": {"pre": [-0.98448, 7.43547, 59.93603], "post": [-21.52236, -5.60519, 27.38107]}, "4.375": {"pre": [-21.52236, -5.60519, 27.38107], "post": [35, 0, 0]}, "4.875": {"pre": [35, 0, 0], "post": [2.21783, -1.15408, 27.47766]}, "5.375": {"pre": [2.21783, -1.15408, 27.47766], "post": [0, 0, 0]}}}}}, "animation.ithiefi_ditsh_alternate.idle": {"loop": true, "animation_length": 3.5834, "bones": {"leftArm": {"rotation": [0, 0, "-5+math.sin(100*q.anim_time) * 3"], "position": [0, "math.sin(100*q.anim_time-80) * 0.5", 0]}, "rightArm": {"rotation": [0, 0, "5-math.sin(100*q.anim_time) * 3"], "position": [0, "math.sin(100*q.anim_time-80) * 0.5", 0]}, "torso": {"position": [0, "math.sin(100*q.anim_time-120) * 0.3", 0]}}}, "animation.ithiefi_ditsh_alternate.walk": {"loop": true, "animation_length": 7.1668, "anim_time_update": "q.anim_time + (q.delta_time * (q.modified_move_speed * 2.4))", "bones": {"head": {"rotation": ["math.cos(800*q.anim_time-80) * 4", 0, 0]}, "leftArm": {"rotation": ["-math.cos(400*q.anim_time) * 24", 0, 0]}, "rightArm": {"rotation": ["math.cos(400*q.anim_time) * 24", 0, 0]}, "leftLeg": {"rotation": ["math.cos(400*q.anim_time) * 24", 0, 0], "position": [0, "math.clamp(math.sin(q.anim_time* 400) * 2,0 , 8)", 0]}, "rightLeg": {"rotation": ["-math.cos(400*q.anim_time) * 24", 0, 0], "position": [0, "math.clamp(-math.sin(q.anim_time* 400) * 2,0 , 8)", 0]}, "root": {"position": [0, "math.sin(800*q.anim_time-80) * 0.75", 0]}, "torso": {"rotation": ["math.cos(q.anim_time * 800) * 1", 0, 0], "position": [0, "math.sin(q.anim_time * 800) * 0.3", 0]}}}}}